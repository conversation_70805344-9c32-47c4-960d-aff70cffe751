'use client';

import React from 'react';
import { Box, Flex, Text, Heading, Avatar, Button, TextField, IconButton } from '@radix-ui/themes';
import { 
  PaperPlaneIcon, 
  SpeakerLoudIcon, 
  GlobeIcon, 
  PersonIcon,
  HamburgerMenuIcon
} from '@radix-ui/react-icons';

// Sample data for the session
const sessionData = {
  id: 'session-123',
  title: 'The Fundamentals of Quantum Physics',
  resources: [
    { name: 'Chapter 1 Notes.pdf', url: '#' },
    { name: 'Key Formulas.png', url: '#' },
  ],
};

// --- The Main Page Component ---
export default function ActiveSessionsPage() {
  
  // Define styles as objects for cleaner JSX
  const pageGridStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: '340px 1fr', // Sidebar width and main content
    gridTemplateRows: 'auto 1fr',     // Header height and main content row
    gridTemplateAreas: `
      "header header"
      "sidebar main"
    `,
    height: '100vh',
    background: 'var(--gray-1)',
  };

  const blackboardStyle: React.CSSProperties = {
    fontFamily: "'Kalam', cursive", // The handwriting font
    width: '100%',
    height: '100%',
    background: '#2c3e50', // A dark, slate-blue for the blackboard
    color: '#ecf0f1',     // An off-white, chalky color for the text
    border: '12px solid #8d6e63', // A 'wooden' frame
    borderRadius: '8px',
    padding: '24px',
    fontSize: '28px',
    lineHeight: '1.6',
    boxShadow: 'inset 0 0 10px rgba(0,0,0,0.5)',
    resize: 'none',
    outline: 'none',
  };

  return (
    <Box style={pageGridStyle}>
      {/* --- 1. Top Header --- */}
      <Flex
        align="center" 
        justify="between" 
        gap="5" 
        style={{ gridArea: 'header', padding: '12px 24px', borderBottom: '1px solid var(--gray-a5)', background: 'var(--color-panel-solid)' }}
      >
        <Heading as="h1" size="5">{sessionData.title}</Heading>
        <Button variant="soft">
          <HamburgerMenuIcon />
          Resources
        </Button>
      </Flex>
      
      {/* --- 2. Left Sidebar (Chat & Controls) --- */}
      <Flex 
        direction="column" 
        style={{ gridArea: 'sidebar', background: 'var(--color-panel-solid)', borderRight: '1px solid var(--gray-a5)', padding: '20px' }}
      >
        {/* Tutor Controls */}
        <Flex direction="column" gap="3" align="center" style={{ paddingBottom: '20px', borderBottom: '1px solid var(--gray-a5)'}}>
            <Avatar 
                src="/path-to-your-ai-avatar.png" // Add a path to an avatar image
                fallback="AI" 
                size="7" 
                radius="full"
            />
            <Text weight="bold" size="4">Your AI Tutor</Text>
            <Flex gap="3" mt="2">
                <Button variant="soft" title="Change Voice"><SpeakerLoudIcon/></Button>
                <Button variant="soft" title="Change Language"><GlobeIcon/></Button>
                <Button variant="soft" title="Change Gender"><PersonIcon/></Button>
            </Flex>
        </Flex>

        {/* Chat Area */}
        <Flex direction="column" style={{ flexGrow: 1, overflowY: 'auto', padding: '16px 0' }}>
            {/* This is where you would map over chat messages */}
            <Text as="p" size="2" color="gray" style={{ textAlign: 'center' }}>Chat history will appear here.</Text>
        </Flex>

        {/* Chat Input */}
        <Flex gap="3" align="center" style={{ marginTop: 'auto' }}>
            <TextField.Root placeholder="Ask a question..." style={{ flexGrow: 1 }}>
                <TextField.Slot/>
            </TextField.Root>
            <IconButton size="2"><PaperPlaneIcon/></IconButton>
        </Flex>
      </Flex>

      {/* --- 3. Main Content (The Blackboard) --- */}
      <Box style={{ gridArea: 'main', padding: '24px' }}>
        <textarea
          style={blackboardStyle}
          defaultValue={`Welcome to Quantum Physics!\n\nLet's start with a foundational concept:\n\nΨ(x, t) = A * e^(i * (k*x - ω*t))\n\nThis is the wave function for a free particle.`}
        />
      </Box>
    </Box>
  );
}