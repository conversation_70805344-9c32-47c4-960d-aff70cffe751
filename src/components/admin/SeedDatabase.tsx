"use client";

import React, { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Button, Text, Box } from "@radix-ui/themes";

export const SeedDatabase = () => {
  const [isSeeding, setIsSeeding] = useState(false);
  const [message, setMessage] = useState("");
  
  const seedLibrary = useMutation(api.seedLibrary.seedLibraryData);

  const handleSeed = async () => {
    setIsSeeding(true);
    setMessage("");
    
    try {
      const result = await seedLibrary();
      setMessage(`✅ ${result.message}${result.itemsCreated ? ` - ${result.itemsCreated} items created` : ""}`);
    } catch (error) {
      setMessage(`❌ Error: ${error}`);
    } finally {
      setIsSeeding(false);
    }
  };

  return (
    <Box style={{ 
      padding: "20px", 
      border: "1px solid var(--gray-6)", 
      borderRadius: "8px",
      margin: "20px",
      maxWidth: "400px"
    }}>
      <Text size="4" style={{ marginBottom: "16px", fontWeight: 600 }}>
        Database Seeding
      </Text>
      
      <Text as="p" size="2" style={{ marginBottom: "16px", color: "var(--gray-11)" }}>
        Click the button below to populate the library with initial data.
      </Text>
      
      <Button 
        onClick={handleSeed} 
        disabled={isSeeding}
        style={{ marginBottom: "16px" }}
      >
        {isSeeding ? "Seeding..." : "Seed Library Data"}
      </Button>
      
      {message && (
        <Text as="p" size="2" style={{ 
          padding: "8px", 
          borderRadius: "4px",
          backgroundColor: message.startsWith("✅") ? "var(--green-2)" : "var(--red-2)",
          color: message.startsWith("✅") ? "var(--green-11)" : "var(--red-11)"
        }}>
          {message}
        </Text>
      )}
    </Box>
  );
};
