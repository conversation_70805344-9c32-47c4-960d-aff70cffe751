import { mutation } from "./_generated/server";

// Mutation to seed the library with initial data
export const seedLibraryData = mutation({
  handler: async (ctx) => {
    // Check if data already exists
    const existingItems = await ctx.db.query("libraryItems").collect();
    if (existingItems.length > 0) {
      console.log("Library data already exists, skipping seed");
      return { message: "Data already exists" };
    }

    const now = Date.now();

    // Live Sessions Data
    const liveSessionsData = [
      {
        title: "Mathématiques Avancées - Calcul Intégral",
        description: "Session interactive avec Dr<PERSON> Martin",
        category: "Live Sessions",
        icon: "📐",
        isLive: true,
        liveViewers: 24,
        duration: "45 min",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Programmation Python pour Débutants",
        description: "Apprenez les bases avec des exemples pratiques",
        category: "Live Sessions",
        icon: "🐍",
        isLive: true,
        liveViewers: 67,
        duration: "1h 20min",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Histoire de l'Art Renaissance",
        description: "Exploration des chefs-d'œuvre de la Renaissance",
        category: "Live Sessions",
        icon: "🎨",
        isLive: true,
        liveViewers: 15,
        duration: "30 min",
        createdAt: now,
        updatedAt: now,
      },
    ];

    // Regular Library Items Data
    const regularItemsData = [
      {
        title: "Les Fondamentaux de la Physique Quantique",
        description: "Créé: 8 Juin 2025 | 5 Sources",
        category: "Sessions de Cours",
        icon: "⚛️",
        sources: 5,
        dateCreated: "8 Juin 2025",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Introduction au Développement Web",
        description: "Créé: 8 Juin 2025 | 5 Sources",
        category: "Sessions de Cours",
        icon: "💻",
        sources: 5,
        dateCreated: "8 Juin 2025",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "L'Essor et la Chute de la Rome Antique",
        description: "Par Mary Beard | 5 Sources",
        category: "Livres",
        icon: "🏛️",
        author: "Mary Beard",
        sources: 5,
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Le Processus de la Photosynthèse",
        description: "Publié dans Nature | 50 Sources",
        category: "Articles",
        icon: "🌻",
        publishedIn: "Nature",
        sources: 50,
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Assistant de Brainstorming",
        description: "Explorez et développez vos idées avec une IA créative.",
        category: "Sessions de Cours",
        icon: "💡",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Gestionnaire de Budget",
        description: "Prenez le contrôle de vos finances—suivez les dépenses et planifiez.",
        category: "Livres",
        icon: "💳",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Coach d'Écriture",
        description: "Obtenez des commentaires clairs et pratiques sur votre écriture.",
        category: "Conseils",
        icon: "✍️",
        createdAt: now,
        updatedAt: now,
      },
      {
        title: "Conseiller de Startup YC",
        description: "Obtenez des conseils pour votre parcours entrepreneurial.",
        category: "Examens Passés",
        icon: "🚀",
        createdAt: now,
        updatedAt: now,
      },
    ];

    // Insert all data
    const allData = [...liveSessionsData, ...regularItemsData];
    
    for (const item of allData) {
      await ctx.db.insert("libraryItems", item);
    }

    return { 
      message: "Library data seeded successfully", 
      itemsCreated: allData.length 
    };
  },
});
