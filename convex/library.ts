import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Query to get all library items
export const getAllLibraryItems = query({
  handler: async (ctx) => {
    return await ctx.db.query("libraryItems").order("desc").collect();
  },
});

// Query to get library items by category
export const getLibraryItemsByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("libraryItems")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .order("desc")
      .collect();
  },
});

// Query to get only live sessions
export const getLiveSessions = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("libraryItems")
      .withIndex("by_isLive", (q) => q.eq("isLive", true))
      .order("desc")
      .collect();
  },
});

// Query to get regular library items (excluding live sessions)
export const getRegularLibraryItems = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("libraryItems")
      .filter((q) => q.neq(q.field("isLive"), true))
      .order("desc")
      .collect();
  },
});

// Query to count live sessions
export const getLiveSessionCount = query({
  handler: async (ctx) => {
    const liveSessions = await ctx.db
      .query("libraryItems")
      .withIndex("by_isLive", (q) => q.eq("isLive", true))
      .collect();
    return liveSessions.length;
  },
});

// Mutation to create a new library item
export const createLibraryItem = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    category: v.string(),
    icon: v.string(),
    isLive: v.optional(v.boolean()),
    liveViewers: v.optional(v.number()),
    duration: v.optional(v.string()),
    sources: v.optional(v.number()),
    author: v.optional(v.string()),
    publishedIn: v.optional(v.string()),
    dateCreated: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    return await ctx.db.insert("libraryItems", {
      ...args,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Mutation to update live session viewer count
export const updateLiveViewers = mutation({
  args: {
    id: v.id("libraryItems"),
    liveViewers: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.id, {
      liveViewers: args.liveViewers,
      updatedAt: Date.now(),
    });
  },
});

// Mutation to delete a library item
export const deleteLibraryItem = mutation({
  args: { id: v.id("libraryItems") },
  handler: async (ctx, args) => {
    return await ctx.db.delete(args.id);
  },
});
