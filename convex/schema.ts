import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  libraryItems: defineTable({
    // Basic item information
    title: v.string(),
    description: v.string(),
    category: v.string(),
    icon: v.string(),
    
    // Live session specific fields (optional)
    isLive: v.optional(v.boolean()),
    liveViewers: v.optional(v.number()),
    duration: v.optional(v.string()),
    
    // Metadata
    createdAt: v.number(),
    updatedAt: v.number(),
    
    // Additional fields for regular library items
    sources: v.optional(v.number()),
    author: v.optional(v.string()),
    publishedIn: v.optional(v.string()),
    dateCreated: v.optional(v.string()),
  })
    .index("by_category", ["category"])
    .index("by_isLive", ["isLive"])
    .index("by_createdAt", ["createdAt"]),
});
